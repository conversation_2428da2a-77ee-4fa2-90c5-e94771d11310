"use client";

import { useState } from "react";

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    role: "Corporate Director of Technical Operations",
    content:
      "I've been involved with CSR in more ways than I was allowed to answer! I've worked with the team to configure roles for Accounts Payable, content marketing, branding, but mainly tech support. I've had nothing but honest and genuine conversations with everyone involved at CSR. Their willingness to learn and to understand the issues that our end users face exceeds any expectation that I had previously. The tech support team has become an integral part of our IT team and has allowed us to focus on larger projects and expanding our business. I am extremely thankful for all the services CSR provides our company.",
    avatar: "<PERSON>",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    role: "Corporate Director of Technical Operations",
    content:
      "I've been involved with CSR in more ways than I was allowed to answer! I've worked with the team to configure roles for Accounts Payable, content marketing, branding, but mainly tech support. I've had nothing but honest and genuine conversations with everyone involved at CSR. Their willingness to learn and to understand the issues that our end users face exceeds any expectation that I had previously. The tech support team has become an integral part of our IT team and has allowed us to focus on larger projects and expanding our business. I am extremely thankful for all the services CSR provides our company.",
    avatar: "<PERSON>",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "IT Director",
    content:
      "Working with CSR has been transformative for our organization. Their expertise in healthcare management and consulting has helped us streamline our operations significantly. The team's dedication to understanding our unique challenges and providing tailored solutions has exceeded our expectations.",
    avatar: "SJ",
  },
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length
    );
  };

  const goToTestimonial = (index) => {
    setCurrentIndex(index);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-block px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
            Testimonials
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            At CSR Ltd,{" "}
            <span className="text-blue-600">
              our client get the advantage of
            </span>
          </h2>
          <p className="text-xl text-gray-900 font-medium">working with a:</p>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-8 mb-12 text-sm text-gray-600">
          <div className="text-center">
            <div className="font-semibold text-gray-900">CONSULTING</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">
              COHESIVE HEALTHCARE MANAGEMENT & CONSULTING
            </div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">
              COHESIVE HEALTHCARE MANAGEMENT
            </div>
          </div>
        </div>

        {/* Testimonials Slider */}
        <div className="relative">
          <div className="flex items-stretch justify-center gap-6 lg:gap-8">
            {/* Left testimonial */}
            <div className="hidden lg:block w-80 opacity-60">
              {currentIndex > 0 && (
                <div className="bg-white p-6 rounded-lg shadow-sm h-full flex flex-col">
                  <div className="text-4xl text-gray-300 mb-4">&ldquo;</div>
                  <p className="text-gray-600 text-sm leading-relaxed mb-6 flex-grow">
                    {testimonials[currentIndex - 1].content.length > 200
                      ? testimonials[currentIndex - 1].content.substring(
                          0,
                          200
                        ) + "..."
                      : testimonials[currentIndex - 1].content}
                  </p>
                  <div className="flex items-center mt-auto">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                      {testimonials[currentIndex - 1].avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 text-sm">
                        {testimonials[currentIndex - 1].name}
                      </div>
                      <div className="text-gray-500 text-xs">
                        {testimonials[currentIndex - 1].role}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Main testimonial */}
            <div className="w-full lg:w-80">
              <div className="bg-white p-6 rounded-lg shadow-lg h-full flex flex-col">
                <div className="text-4xl text-gray-300 mb-4">&ldquo;</div>
                <p className="text-gray-700 text-sm leading-relaxed mb-6 flex-grow">
                  {testimonials[currentIndex].content}
                </p>
                <div className="flex items-center mt-auto">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                    {testimonials[currentIndex].avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">
                      {testimonials[currentIndex].name}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {testimonials[currentIndex].role}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right testimonial */}
            <div className="hidden lg:block w-80 opacity-60">
              {currentIndex < testimonials.length - 1 && (
                <div className="bg-white p-6 rounded-lg shadow-sm h-full flex flex-col">
                  <div className="text-4xl text-gray-300 mb-4">&ldquo;</div>
                  <p className="text-gray-600 text-sm leading-relaxed mb-6 flex-grow">
                    {testimonials[currentIndex + 1].content.length > 200
                      ? testimonials[currentIndex + 1].content.substring(
                          0,
                          200
                        ) + "..."
                      : testimonials[currentIndex + 1].content}
                  </p>
                  <div className="flex items-center mt-auto">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                      {testimonials[currentIndex + 1].avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 text-sm">
                        {testimonials[currentIndex + 1].name}
                      </div>
                      <div className="text-gray-500 text-xs">
                        {testimonials[currentIndex + 1].role}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center mt-8 space-x-6">
            <button
              onClick={prevTestimonial}
              className="w-8 h-8 rounded-full border border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center disabled:opacity-50"
              disabled={currentIndex === 0}
            >
              <svg
                className="w-4 h-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {/* Dots indicator */}
            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentIndex ? "bg-blue-600" : "bg-gray-300"
                  }`}
                />
              ))}
            </div>

            <button
              onClick={nextTestimonial}
              className="w-8 h-8 rounded-full border border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center disabled:opacity-50"
              disabled={currentIndex === testimonials.length - 1}
            >
              <svg
                className="w-4 h-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          {/* Page indicator */}
          <div className="text-center mt-4">
            <span className="text-sm text-gray-500">
              {currentIndex + 1}/{testimonials.length}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}
